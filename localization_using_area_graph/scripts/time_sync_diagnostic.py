#!/usr/bin/env python3
"""
时间同步诊断工具
用于检测和诊断ROS2系统中的时间同步问题
"""

import rclpy
from rclpy.node import Node
from rclpy.time import Time
from builtin_interfaces.msg import Time as TimeMsg
from rosgraph_msgs.msg import Clock
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
import time
import threading
from collections import defaultdict, deque

class TimeSyncDiagnostic(Node):
    def __init__(self):
        super().__init__('time_sync_diagnostic')
        
        # 声明参数
        self.declare_parameter('check_duration', 30.0)
        self.declare_parameter('time_tolerance', 0.1)
        self.declare_parameter('use_sim_time', True)
        
        self.check_duration = self.get_parameter('check_duration').value
        self.time_tolerance = self.get_parameter('time_tolerance').value
        self.use_sim_time = self.get_parameter('use_sim_time').value
        
        # 时间戳记录
        self.timestamps = defaultdict(deque)
        self.clock_times = deque(maxlen=100)
        self.system_times = deque(maxlen=100)
        
        # 统计信息
        self.message_counts = defaultdict(int)
        self.time_jumps = []
        self.last_clock_time = None
        
        # 创建订阅者
        self.create_subscriptions()
        
        # 创建定时器进行周期性检查
        self.timer = self.create_timer(1.0, self.periodic_check)
        
        # 创建诊断定时器
        self.diagnostic_timer = self.create_timer(5.0, self.print_diagnostic)
        
        self.get_logger().info(f"时间同步诊断工具启动")
        self.get_logger().info(f"use_sim_time: {self.use_sim_time}")
        self.get_logger().info(f"检查持续时间: {self.check_duration}秒")
        self.get_logger().info(f"时间容差: {self.time_tolerance}秒")
        
        # 启动检查线程
        self.start_time = time.time()
        
    def create_subscriptions(self):
        """创建各种话题的订阅者来监控时间戳"""
        
        # 监控/clock话题
        self.clock_sub = self.create_subscription(
            Clock, '/clock', self.clock_callback, 10)
        
        # 监控关键话题的时间戳
        topics_to_monitor = [
            ('/mapPC_AG', PointCloud2),
            ('/hesai/pandar', PointCloud2),
            ('/cloud_handler/pose', PoseStamped),
        ]
        
        for topic, msg_type in topics_to_monitor:
            self.create_subscription(
                msg_type, topic, 
                lambda msg, t=topic: self.message_callback(msg, t), 10)
    
    def clock_callback(self, msg):
        """处理/clock话题消息"""
        current_system_time = time.time()
        clock_time = msg.clock.sec + msg.clock.nanosec * 1e-9
        
        self.clock_times.append(clock_time)
        self.system_times.append(current_system_time)
        
        # 检测时间跳跃
        if self.last_clock_time is not None:
            time_diff = clock_time - self.last_clock_time
            if abs(time_diff) > self.time_tolerance:
                jump_info = {
                    'timestamp': current_system_time,
                    'from': self.last_clock_time,
                    'to': clock_time,
                    'diff': time_diff
                }
                self.time_jumps.append(jump_info)
                self.get_logger().warn(
                    f"检测到时间跳跃: {self.last_clock_time:.3f} -> {clock_time:.3f} "
                    f"(差值: {time_diff:.3f}秒)")
        
        self.last_clock_time = clock_time
    
    def message_callback(self, msg, topic):
        """处理其他话题的消息，记录时间戳"""
        if hasattr(msg, 'header') and hasattr(msg.header, 'stamp'):
            stamp = msg.header.stamp
            timestamp = stamp.sec + stamp.nanosec * 1e-9
            current_time = time.time()
            
            self.timestamps[topic].append({
                'ros_time': timestamp,
                'system_time': current_time,
                'message_count': self.message_counts[topic]
            })
            
            # 保持队列大小
            if len(self.timestamps[topic]) > 50:
                self.timestamps[topic].popleft()
            
            self.message_counts[topic] += 1
    
    def periodic_check(self):
        """周期性检查时间同步状态"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        if elapsed > self.check_duration:
            self.print_final_report()
            rclpy.shutdown()
    
    def print_diagnostic(self):
        """打印诊断信息"""
        self.get_logger().info("=== 时间同步诊断报告 ===")
        
        # ROS时间 vs 系统时间
        if self.clock_times and self.system_times:
            latest_clock = self.clock_times[-1]
            latest_system = self.system_times[-1]
            self.get_logger().info(f"最新ROS时间: {latest_clock:.3f}")
            self.get_logger().info(f"最新系统时间: {latest_system:.3f}")
            self.get_logger().info(f"时间差: {abs(latest_clock - latest_system):.3f}秒")
        
        # 消息计数
        for topic, count in self.message_counts.items():
            self.get_logger().info(f"{topic}: {count} 条消息")
        
        # 时间跳跃统计
        if self.time_jumps:
            self.get_logger().warn(f"检测到 {len(self.time_jumps)} 次时间跳跃")
            for jump in self.time_jumps[-3:]:  # 显示最近3次跳跃
                self.get_logger().warn(
                    f"  跳跃: {jump['from']:.3f} -> {jump['to']:.3f} "
                    f"(差值: {jump['diff']:.3f})")
        else:
            self.get_logger().info("未检测到时间跳跃")
        
        # 检查节点参数
        try:
            use_sim_time = self.get_parameter('use_sim_time').value
            self.get_logger().info(f"当前节点use_sim_time: {use_sim_time}")
        except:
            self.get_logger().warn("无法获取use_sim_time参数")
        
        self.get_logger().info("========================")
    
    def print_final_report(self):
        """打印最终报告"""
        self.get_logger().info("=== 最终时间同步诊断报告 ===")
        
        # 总体统计
        total_messages = sum(self.message_counts.values())
        self.get_logger().info(f"总消息数: {total_messages}")
        self.get_logger().info(f"总时间跳跃数: {len(self.time_jumps)}")
        
        # 时间跳跃分析
        if self.time_jumps:
            self.get_logger().error("发现时间同步问题！")
            self.get_logger().error("建议检查以下项目：")
            self.get_logger().error("1. 所有节点的use_sim_time参数设置")
            self.get_logger().error("2. rosbag播放的--clock参数")
            self.get_logger().error("3. 节点启动顺序")
            self.get_logger().error("4. TF发布器的时间同步配置")
        else:
            self.get_logger().info("时间同步正常！")
        
        # 话题时间戳一致性检查
        for topic, timestamps in self.timestamps.items():
            if len(timestamps) > 1:
                time_diffs = []
                for i in range(1, len(timestamps)):
                    diff = timestamps[i]['ros_time'] - timestamps[i-1]['ros_time']
                    time_diffs.append(diff)
                
                if time_diffs:
                    avg_diff = sum(time_diffs) / len(time_diffs)
                    max_diff = max(time_diffs)
                    min_diff = min(time_diffs)
                    
                    self.get_logger().info(
                        f"{topic} 时间戳间隔: 平均={avg_diff:.3f}s, "
                        f"最大={max_diff:.3f}s, 最小={min_diff:.3f}s")

def main(args=None):
    rclpy.init(args=args)
    
    diagnostic = TimeSyncDiagnostic()
    
    try:
        rclpy.spin(diagnostic)
    except KeyboardInterrupt:
        diagnostic.get_logger().info("诊断工具被用户中断")
    finally:
        diagnostic.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
